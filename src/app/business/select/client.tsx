"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Image from "next/image";

type Tenant = {
  tenant_id: string;
  role: string;
  tenants: {
    id: string;
    name: string;
    slug: string;
    logo_url: string | null;
  };
};

interface BusinessSelectionClientProps {
  userId: string;
}

export function BusinessSelectionClient({
  userId,
}: BusinessSelectionClientProps) {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        setIsLoading(true);
        console.log("Fetching tenants for user ID:", userId);

        // Use the API endpoint to fetch tenants
        const response = await fetch("/api/user/tenants");

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Error fetching tenants from API:", errorData);
          toast.error("Failed to load your businesses");
          setTenants([]);
          return;
        }

        const data = await response.json();
        console.log("Tenants data from API:", data);

        if (!data.tenants || data.tenants.length === 0) {
          console.log("No tenants found for user");
          setTenants([]);
          setIsLoading(false);
          return;
        }

        setTenants(data.tenants);

        // If there's only one tenant, redirect to it automatically
        if (data.tenants.length === 1) {
          router.push(`/tenant/${data.tenants[0].tenants.slug}`);
        }
      } catch (error) {
        console.error("Error fetching tenants:", error);
        toast.error("Failed to load your businesses");
        setTenants([]);
      } finally {
        setIsLoading(false);
      }
    };

    if (userId) {
      fetchTenants();
    } else {
      console.error("No userId provided to BusinessSelectionClient");
      setIsLoading(false);
    }
  }, [userId, router]);

  if (isLoading) {
    return (
      <div className="flex min-h-[60vh] items-center justify-center">
        <div className="text-center">
          <p className="text-lg font-medium">Loading your businesses...</p>
          <p className="text-sm text-muted-foreground">
            Please wait while we fetch your businesses
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-6xl py-10">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold">Select a Business</h1>
        <p className="text-muted-foreground">
          Choose a business to manage or create a new one
        </p>
      </div>

      {tenants.length === 0 ? (
        <Card className="mx-auto max-w-md">
          <CardHeader>
            <CardTitle>Get Started</CardTitle>
            <CardDescription>
              You don&apos;t have any businesses yet. Create your first business
              to get started.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/tenant/create">Create Business</Link>
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {tenants.map((tenant) => (
            <Card key={tenant.id} className="flex flex-col">
              <CardHeader>
                <CardTitle>{tenant.tenants.name}</CardTitle>
                <CardDescription>
                  Role:{" "}
                  {tenant.role.charAt(0).toUpperCase() + tenant.role.slice(1)}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow">
                {tenant.tenants.logo_url && (
                  <div className="mb-4 flex justify-center">
                    <Image
                      src={tenant.tenants.logo_url}
                      alt={`${tenant.tenants.name} logo`}
                      width={64}
                      height={64}
                      className="h-16 w-16 rounded-full object-cover"
                    />
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full">
                  <Link href={`/tenant/${tenant.tenants.slug}`}>
                    Select Business
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}

          <Card className="flex flex-col border-dashed">
            <CardHeader>
              <CardTitle>Create New Business</CardTitle>
              <CardDescription>
                Add another business to your account
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow"></CardContent>
            <CardFooter>
              <Button asChild variant="outline" className="w-full">
                <Link href="/tenant/create">Create Business</Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  );
}
