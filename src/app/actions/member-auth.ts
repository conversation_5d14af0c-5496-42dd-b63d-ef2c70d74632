"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { cache } from "react";
import { prisma } from "@/lib/prisma";
import { createClient } from "@/utils/supabase/server";
// Define the member login schema for validation
const memberLoginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

// Define the member password reset schema for validation
const memberPasswordResetSchema = z.object({
  email: z.string().email("Invalid email address"),
});

// Define the member password update schema for validation
const memberPasswordUpdateSchema = z
  .object({
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z
      .string()
      .min(6, "Password must be at least 6 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

/**
 * Get a member by email
 * This function is cached to improve performance
 */
export const getMemberByEmail = cache(async (email: string) => {
  try {
    // Get the member by email using Prisma
    const member = await prisma.member.findUnique({
      where: { email },
      include: {
        tenantMembers: {
          include: {
            tenant: {
              select: {
                id: true,
                name: true,
                slug: true,
                logoUrl: true,
              },
            },
          },
        },
      },
    });

    if (!member) {
      throw new Error("Member not found");
    }

    return { success: true, data: member };
  } catch (error) {
    console.error("Error getting member by email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get member",
      data: null,
    };
  }
});

/**
 * Get member by auth ID
 * This function is cached to improve performance
 */
export const getMemberByAuthId = cache(async (authId: string) => {
  try {
    // Get the member by auth ID using Prisma
    const member = await prisma.member.findFirst({
      where: { authId },
      include: {
        tenantMembers: {
          include: {
            tenant: {
              select: {
                id: true,
                name: true,
                slug: true,
                logoUrl: true,
              },
            },
          },
        },
      },
    });

    if (!member) {
      throw new Error("Member not found");
    }

    // Check if there are any tenant associations
    if (member.tenantMembers.length === 0) {
      throw new Error("Member has no valid tenant associations");
    }

    // Use the first tenant as the default
    const defaultTenantMember = member.tenantMembers[0];

    // Transform the data to match the expected format for compatibility
    const enrichedMember = {
      ...member,
      tenant_id: defaultTenantMember.tenantId,
      status: defaultTenantMember.status,
      tenants: {
        id: defaultTenantMember.tenant.id,
        name: defaultTenantMember.tenant.name,
        slug: defaultTenantMember.tenant.slug,
        logo_url: defaultTenantMember.tenant.logoUrl,
      },
      all_tenants: member.tenantMembers.map((tm) => ({
        id: tm.tenant.id,
        name: tm.tenant.name,
        slug: tm.tenant.slug,
        logo_url: tm.tenant.logoUrl,
        status: tm.status,
        tenant_id: tm.tenantId,
      })),
      all_memberships: member.tenantMembers.map((tm) => ({
        ...tm,
        tenant_id: tm.tenantId,
        member_id: tm.memberId,
        tenant: {
          id: tm.tenant.id,
          name: tm.tenant.name,
          slug: tm.tenant.slug,
          logo_url: tm.tenant.logoUrl,
        },
      })),
    };

    return { success: true, data: enrichedMember };
  } catch (error) {
    console.error("Error getting member by auth ID:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get member",
      data: null,
    };
  }
});

/**
 * Create an auth account for a member
 */
export async function createMemberAuth(
  memberId: string,
  email: string,
  password: string
) {
  try {
    // Check if the member already has an auth account using Prisma
    const member = await prisma.member.findUnique({
      where: { id: memberId },
    });

    if (!member) {
      throw new Error("Member not found");
    }

    if (member.authId) {
      throw new Error("Member already has an auth account");
    }

    // Create a Supabase client for auth operations
    const supabase = await createClient();

    // Use our custom function to create the member auth
    const { data: result, error: fnError } = await supabase.rpc(
      "create_member_auth" as any,
      {
        p_email: email,
        p_password: password,
        p_member_id: memberId,
      }
    );

    if (fnError) {
      throw new Error(`Failed to create member auth: ${fnError.message}`);
    }

    // Get the updated member using Prisma
    const updatedMember = await prisma.member.findUnique({
      where: { id: memberId },
    });

    if (!updatedMember) {
      throw new Error("Failed to get updated member");
    }

    return { success: true, data: updatedMember };
  } catch (error) {
    console.error("Error creating member auth:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create member auth",
    };
  }
}

/**
 * Send a password reset email to a member
 */
export async function sendMemberPasswordReset(email: string) {
  try {
    // Validate the email
    const validatedData = memberPasswordResetSchema.parse({ email });

    // Create a Supabase client
    const supabase = await createClient();

    // Send the password reset email
    const { error } = await supabase.auth.resetPasswordForEmail(
      validatedData.email,
      {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/member/reset-password/callback`,
      }
    );

    if (error) {
      throw new Error(`Failed to send password reset email: ${error.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending password reset email:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to send password reset email",
    };
  }
}

/**
 * Update a member's password
 */
export async function updateMemberPassword(
  data: z.infer<typeof memberPasswordUpdateSchema>
) {
  try {
    // Validate the data
    const validatedData = memberPasswordUpdateSchema.parse(data);

    // Create a Supabase client
    const supabase = await createClient();

    // Update the password
    const { error } = await supabase.auth.updateUser({
      password: validatedData.password,
    });

    if (error) {
      throw new Error(`Failed to update password: ${error.message}`);
    }

    // Update the member's password_set flag using Prisma
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      throw new Error("Authentication required");
    }

    await prisma.member.updateMany({
      where: { authId: userData.user.id },
      data: { passwordSet: true },
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating password:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update password",
    };
  }
}
