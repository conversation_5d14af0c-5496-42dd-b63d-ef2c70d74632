"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { cache } from "react";
import { prisma } from "@/lib/prisma";
import {
  getCurrentUser,
  getCurrentUserWithTenants,
  verifyTenantAccess,
} from "@/lib/auth-prisma";

// Define the tenant schema for validation
const tenantSchema = z.object({
  name: z.string().min(1, "Tenant name is required"),
  slug: z.string().min(1, "Slug is required"),
  logoUrl: z.string().optional(),
  publicPageEnabled: z.boolean().optional(),
  publicPageTitle: z.string().optional(),
  publicPageDescription: z.string().optional(),
});

type TenantData = z.infer<typeof tenantSchema>;

/**
 * Create a new tenant
 */
export async function createTenant(data: TenantData) {
  try {
    // Validate the data
    const validatedData = tenantSchema.parse(data);

    // Get the current user
    const user = await getCurrentUser();

    // Create the tenant using Prisma transaction
    const result = await prisma.$transaction(async (tx) => {
      // Insert the tenant
      const tenant = await tx.tenant.create({
        data: {
          name: validatedData.name,
          slug: validatedData.slug,
          logoUrl: validatedData.logoUrl,
          ownerId: user.id,
          publicPageEnabled: validatedData.publicPageEnabled ?? false,
          publicPageTitle: validatedData.publicPageTitle,
          publicPageDescription: validatedData.publicPageDescription,
        },
      });

      // Create a tenant_user record for the owner
      await tx.tenantUser.create({
        data: {
          tenantId: tenant.id,
          userId: user.id,
          role: "admin",
        },
      });

      return tenant;
    });

    // Revalidate the business selection page
    revalidatePath("/business/select");

    return { success: true, data: result };
  } catch (error) {
    console.error("Error creating tenant:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create tenant",
    };
  }
}

/**
 * Update an existing tenant
 */
export async function updateTenant(id: string, data: Partial<TenantData>) {
  try {
    // Get the current user and verify tenant access
    const { tenant } = await verifyTenantAccess(id);

    // Update the tenant using Prisma
    const updatedTenant = await prisma.tenant.update({
      where: { id },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.slug && { slug: data.slug }),
        ...(data.logoUrl !== undefined && { logoUrl: data.logoUrl }),
        ...(data.publicPageEnabled !== undefined && {
          publicPageEnabled: data.publicPageEnabled,
        }),
        ...(data.publicPageTitle !== undefined && {
          publicPageTitle: data.publicPageTitle,
        }),
        ...(data.publicPageDescription !== undefined && {
          publicPageDescription: data.publicPageDescription,
        }),
      },
    });

    // Revalidate the tenant pages
    revalidatePath("/business/select");
    revalidatePath(`/tenant/${updatedTenant.slug}`);

    return { success: true, data: updatedTenant };
  } catch (error) {
    console.error("Error updating tenant:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update tenant",
    };
  }
}

/**
 * Get all tenants for the current user
 * This function is cached to improve performance
 */
export const getUserTenants = cache(async () => {
  try {
    // Get the current user with tenant associations
    const userWithTenants = await getCurrentUserWithTenants();

    // Transform the data to match the expected format
    const data = userWithTenants.tenantUsers.map((tenantUser) => ({
      ...tenantUser,
      tenants: tenantUser.tenant,
    }));

    return { success: true, data };
  } catch (error) {
    console.error("Error getting tenants:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get tenants",
      data: [],
    };
  }
});

/**
 * Get a tenant by slug
 * This function is cached to improve performance
 */
export const getTenantBySlug = cache(async (slug: string) => {
  try {
    // Get the tenant using Prisma
    const tenant = await prisma.tenant.findUnique({
      where: { slug },
    });

    if (!tenant) {
      throw new Error("Tenant not found");
    }

    return { success: true, data: tenant };
  } catch (error) {
    console.error("Error getting tenant:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get tenant",
      data: null,
    };
  }
});

/**
 * Update the public page settings for a tenant
 */
export async function updatePublicPageSettings(
  tenantId: string,
  data: {
    publicPageEnabled: boolean;
    publicPageTitle?: string;
    publicPageDescription?: string;
  }
) {
  try {
    // Get the current user and verify tenant access
    const { tenant } = await verifyTenantAccess(tenantId);

    // Update the tenant's public page settings using Prisma
    const updatedTenant = await prisma.tenant.update({
      where: { id: tenantId },
      data: {
        publicPageEnabled: data.publicPageEnabled,
        publicPageTitle: data.publicPageTitle,
        publicPageDescription: data.publicPageDescription,
      },
    });

    // Revalidate the tenant pages
    revalidatePath(`/tenant/${updatedTenant.slug}`);
    revalidatePath(`/tenant/${updatedTenant.slug}/settings/public-page`);
    // Also revalidate the public page if it exists
    if (updatedTenant.publicPageEnabled) {
      revalidatePath(`/p/${updatedTenant.slug}`);
    }

    return { success: true, data: updatedTenant };
  } catch (error) {
    console.error("Error updating public page settings:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update public page settings",
    };
  }
}
