"use server";

import { cache } from "react";
import { prisma } from "@/lib/prisma";

/**
 * Get a tenant by slug for public access
 * This function is cached to improve performance
 */
export const getPublicTenant = cache(async (slug: string) => {
  try {
    // Get the tenant by slug with public page enabled
    const tenant = await prisma.tenant.findUnique({
      where: {
        slug,
        publicPageEnabled: true,
      },
    });

    // If tenant not found or public page not enabled, return error
    if (!tenant) {
      return { success: false, error: "Public page not enabled", data: null };
    }

    return { success: true, data: tenant };
  } catch (error) {
    console.error("Error getting public tenant:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get tenant",
      data: null,
    };
  }
});

/**
 * Get all active subscription plans for a tenant for public access
 * This function is cached to improve performance
 */
export const getPublicPlans = cache(async (tenantId: string) => {
  try {
    // Get all active plans for the tenant with public page enabled
    const plans = await prisma.subscriptionPlan.findMany({
      where: {
        tenantId,
        isActive: true,
        tenant: {
          publicPageEnabled: true,
        },
      },
      orderBy: { price: "asc" },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedPlans = plans.map((plan) => ({
      ...plan,
      price: Number(plan.price),
    }));

    return { success: true, data: serializedPlans };
  } catch (error) {
    console.error("Error getting public plans:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get plans",
      data: [],
    };
  }
});
