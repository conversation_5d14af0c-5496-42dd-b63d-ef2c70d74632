"use client";

import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { formatCurrency } from "@/lib/utils";
import { PlusCircle, Search } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { Badge } from "@/components/ui/badge";

interface PlansClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  plans: any[];
}

export default function PlansClient({
  user,
  tenant,
  tenants,
  plans,
}: PlansClientProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  // Filter plans based on search query
  const filteredPlans = plans.filter((plan) => {
    const name = plan.name.toLowerCase();
    const description = plan.description?.toLowerCase() || "";
    const query = searchQuery.toLowerCase();

    return name.includes(query) || description.includes(query);
  });

  // Get billing cycle display
  const getBillingCycleDisplay = (cycle: string) => {
    switch (cycle) {
      case "monthly":
        return "Monthly";
      case "quarterly":
        return "Quarterly";
      case "biannual":
        return "Bi-Annual";
      case "annual":
        return "Annual";
      default:
        return cycle;
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold">Subscription Plans</h1>
                <p className="text-muted-foreground">
                  Manage your membership plans
                </p>
              </div>
              <Button asChild>
                <Link href={`/tenant/${tenant.slug}/plans/new`}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Plan
                </Link>
              </Button>
            </div>

            <Card className="mb-6">
              <CardHeader className="pb-3">
                <CardTitle>Search Plans</CardTitle>
                <CardDescription>
                  Find plans by name or description
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle>All Plans</CardTitle>
                <CardDescription>
                  {filteredPlans.length} plans found
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Billing Cycle</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPlans.length > 0 ? (
                      filteredPlans.map((plan) => (
                        <TableRow key={plan.id}>
                          <TableCell className="font-medium">
                            {plan.name}
                          </TableCell>
                          <TableCell>{formatCurrency(plan.price)}</TableCell>
                          <TableCell>
                            {getBillingCycleDisplay(plan.billingCycle)}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={`${
                                plan.isActive ? "bg-green-500" : "bg-gray-500"
                              } text-white`}
                            >
                              {plan.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                router.push(
                                  `/tenant/${tenant.slug}/plans/${plan.id}`
                                )
                              }
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6">
                          No plans found. Add your first plan to get started.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
