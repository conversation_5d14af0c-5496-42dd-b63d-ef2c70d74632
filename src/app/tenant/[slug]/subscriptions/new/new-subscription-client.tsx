"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { ArrowLeft, CalendarIcon } from "lucide-react";
import { format, addMonths, addDays } from "date-fns";
import { createSubscription } from "@/app/actions/subscriptions";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { subscriptionSchema } from "@/lib/validations";
// import { createSubscription } from "@/lib/subscriptions";
import { formatCurrency } from "@/lib/utils";

type NewSubscriptionFormValues = z.infer<typeof subscriptionSchema>;

interface NewSubscriptionClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  members: any[];
  plans: any[];
  preselectedMemberId?: string;
}

export default function NewSubscriptionClient({
  user,
  tenant,
  tenants,
  members,
  plans,
  preselectedMemberId,
}: NewSubscriptionClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<any>(null);

  // Initialize form with default values
  const form = useForm<NewSubscriptionFormValues>({
    resolver: zodResolver(subscriptionSchema),
    defaultValues: {
      memberId: preselectedMemberId || "",
      planId: "",
      startDate: format(new Date(), "yyyy-MM-dd"),
      status: "active",
      autoRenew: true,
    },
  });

  // Watch planId to update selected plan
  const watchPlanId = form.watch("planId");

  // Update selected plan when plan_id changes
  useEffect(() => {
    if (watchPlanId) {
      const plan = plans.find((p) => p.id === watchPlanId);
      setSelectedPlan(plan);

      // Calculate end date based on billing cycle
      if (plan) {
        const startDate = new Date(form.getValues("start_date"));
        let endDate;

        switch (plan.billing_cycle) {
          case "monthly":
            endDate = addMonths(startDate, 1);
            break;
          case "quarterly":
            endDate = addMonths(startDate, 3);
            break;
          case "biannual":
            endDate = addMonths(startDate, 6);
            break;
          case "annual":
            endDate = addMonths(startDate, 12);
            break;
          default:
            endDate = addMonths(startDate, 1);
        }

        // Subtract one day to make it inclusive
        endDate = addDays(endDate, -1);

        form.setValue("end_date", format(endDate, "yyyy-MM-dd"));
      }
    }
  }, [watchPlanId, plans, form]);

  // Watch startDate to update endDate
  const watchStartDate = form.watch("startDate");

  // Update end date when start date changes
  useEffect(() => {
    if (watchStartDate && selectedPlan) {
      const startDate = new Date(watchStartDate);
      let endDate;

      switch (selectedPlan.billingCycle) {
        case "monthly":
          endDate = addMonths(startDate, 1);
          break;
        case "quarterly":
          endDate = addMonths(startDate, 3);
          break;
        case "biannual":
          endDate = addMonths(startDate, 6);
          break;
        case "annual":
          endDate = addMonths(startDate, 12);
          break;
        default:
          endDate = addMonths(startDate, 1);
      }

      // Subtract one day to make it inclusive
      endDate = addDays(endDate, -1);

      form.setValue("endDate", format(endDate, "yyyy-MM-dd"));
    }
  }, [watchStartDate, selectedPlan, form]);

  async function onSubmit(data: NewSubscriptionFormValues) {
    setIsLoading(true);

    try {
      // Convert status to match the enum in the server action
      const statusMap: Record<string, string> = {
        active: "active",
        pending: "pending",
        canceled: "cancelled", // Note the spelling difference
        expired: "inactive",
      };

      // Pass the current tenant_id to help identify which tenant association to use
      const result = await createSubscription({
        ...data,
        status: statusMap[data.status] as any,
        current_tenant_id: tenant.id, // Add the current tenant ID
      });

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Subscription created successfully");
      router.push(`/tenant/${tenant.slug}/subscriptions`);
    } catch (error) {
      console.error("Error creating subscription:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create subscription"
      );
    } finally {
      setIsLoading(false);
    }
  }

  // Function to get billing cycle display
  const getBillingCycleDisplay = (cycle: string) => {
    switch (cycle) {
      case "monthly":
        return "Monthly";
      case "quarterly":
        return "Quarterly";
      case "biannual":
        return "Bi-annual";
      case "annual":
        return "Annual";
      default:
        return cycle;
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <Button
              variant="ghost"
              onClick={() =>
                router.push(`/tenant/${tenant.slug}/subscriptions`)
              }
              className="mb-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Subscriptions
            </Button>

            <div className="mb-6">
              <h1 className="text-3xl font-bold">New Subscription</h1>
              <p className="text-muted-foreground">
                Create a new subscription for a member
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Subscription Details</CardTitle>
                    <CardDescription>
                      Enter the details for the new subscription
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6"
                      >
                        <FormField
                          control={form.control}
                          name="memberId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Member</FormLabel>
                              <Select
                                disabled={isLoading || !!preselectedMemberId}
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a member" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {members.map((member) => (
                                    <SelectItem
                                      key={member.id}
                                      value={member.id}
                                    >
                                      {member.firstName} {member.lastName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Select the member for this subscription
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="planId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Subscription Plan</FormLabel>
                              <Select
                                disabled={isLoading}
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a plan" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {plans.map((plan) => (
                                    <SelectItem key={plan.id} value={plan.id}>
                                      {plan.name} - {formatCurrency(plan.price)}
                                      /
                                      {getBillingCycleDisplay(
                                        plan.billingCycle
                                      ).toLowerCase()}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Select the subscription plan
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="startDate"
                          render={({ field }) => (
                            <FormItem className="flex flex-col">
                              <FormLabel>Start Date</FormLabel>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      variant={"outline"}
                                      className={`w-full pl-3 text-left font-normal ${
                                        !field.value
                                          ? "text-muted-foreground"
                                          : ""
                                      }`}
                                    >
                                      {field.value ? (
                                        format(new Date(field.value), "PPP")
                                      ) : (
                                        <span>Pick a date</span>
                                      )}
                                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent
                                  className="w-auto p-0"
                                  align="start"
                                >
                                  <Calendar
                                    mode="single"
                                    selected={
                                      field.value
                                        ? new Date(field.value)
                                        : undefined
                                    }
                                    onSelect={(date) =>
                                      field.onChange(
                                        date ? format(date, "yyyy-MM-dd") : ""
                                      )
                                    }
                                    disabled={(date) =>
                                      date < new Date("1900-01-01")
                                    }
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                              <FormDescription>
                                When the subscription starts
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="status"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Status</FormLabel>
                              <Select
                                disabled={isLoading}
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a status" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="active">Active</SelectItem>
                                  <SelectItem value="pending">
                                    Pending
                                  </SelectItem>
                                  <SelectItem value="canceled">
                                    Canceled
                                  </SelectItem>
                                  <SelectItem value="expired">
                                    Expired
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Current status of the subscription
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="autoRenew"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">
                                  Auto Renew
                                </FormLabel>
                                <FormDescription>
                                  Automatically renew this subscription when it
                                  expires
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={isLoading}
                        >
                          {isLoading ? "Creating..." : "Create Subscription"}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </div>

              {selectedPlan && (
                <div>
                  <Card>
                    <CardHeader>
                      <CardTitle>Plan Summary</CardTitle>
                      <CardDescription>Selected plan details</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-medium">Name</h3>
                        <p>{selectedPlan.name}</p>
                      </div>
                      <div>
                        <h3 className="font-medium">Price</h3>
                        <p className="text-xl font-bold">
                          {formatCurrency(selectedPlan.price)}
                          <span className="text-sm text-muted-foreground ml-1">
                            /
                            {getBillingCycleDisplay(
                              selectedPlan.billingCycle
                            ).toLowerCase()}
                          </span>
                        </p>
                      </div>
                      <div>
                        <h3 className="font-medium">Billing Cycle</h3>
                        <p>
                          {getBillingCycleDisplay(selectedPlan.billingCycle)}
                        </p>
                      </div>
                      {selectedPlan.description && (
                        <div>
                          <h3 className="font-medium">Description</h3>
                          <p className="text-sm text-muted-foreground">
                            {selectedPlan.description}
                          </p>
                        </div>
                      )}
                      {selectedPlan.features &&
                        selectedPlan.features.length > 0 && (
                          <div>
                            <h3 className="font-medium">Features</h3>
                            <ul className="list-disc list-inside text-sm text-muted-foreground">
                              {selectedPlan.features.map(
                                (feature: string, index: number) => (
                                  <li key={index}>{feature}</li>
                                )
                              )}
                            </ul>
                          </div>
                        )}
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
