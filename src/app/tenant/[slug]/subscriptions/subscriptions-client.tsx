"use client";

import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { formatDate, formatCurrency } from "@/lib/utils";
import { PlusCircle, Search } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { Badge } from "@/components/ui/badge";

interface SubscriptionsClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  subscriptions: any[];
}

export default function SubscriptionsClient({
  user,
  tenant,
  tenants,
  subscriptions,
}: SubscriptionsClientProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  // Filter subscriptions based on search query
  const filteredSubscriptions = subscriptions.filter((subscription) => {
    const memberName =
      `${subscription.member.firstName} ${subscription.member.lastName}`.toLowerCase();
    const planName = subscription.plan.name.toLowerCase();
    const query = searchQuery.toLowerCase();

    return memberName.includes(query) || planName.includes(query);
  });

  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "canceled":
        return "bg-orange-500";
      case "expired":
        return "bg-red-500";
      case "pending":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Function to get billing cycle display
  const getBillingCycleDisplay = (cycle: string) => {
    switch (cycle) {
      case "monthly":
        return "Monthly";
      case "quarterly":
        return "Quarterly";
      case "biannual":
        return "Bi-annual";
      case "annual":
        return "Annual";
      default:
        return cycle;
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold">Subscriptions</h1>
                <p className="text-muted-foreground">
                  Manage member subscriptions
                </p>
              </div>
              <Button asChild>
                <Link href={`/tenant/${tenant.slug}/subscriptions/new`}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Subscription
                </Link>
              </Button>
            </div>

            <div className="mb-6">
              <Input
                placeholder="Search subscriptions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-md"
                prefix={<Search className="h-4 w-4 text-muted-foreground" />}
              />
            </div>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle>All Subscriptions</CardTitle>
                <CardDescription>
                  {filteredSubscriptions.length} subscriptions found
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Member</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Auto Renew</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSubscriptions.length > 0 ? (
                      filteredSubscriptions.map((subscription) => (
                        <TableRow
                          key={subscription.id}
                          className="cursor-pointer"
                          onClick={() =>
                            router.push(
                              `/tenant/${tenant.slug}/subscriptions/${subscription.id}`
                            )
                          }
                        >
                          <TableCell className="font-medium">
                            {subscription.member.firstName}{" "}
                            {subscription.member.lastName}
                          </TableCell>
                          <TableCell>{subscription.plan.name}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={`${getStatusColor(
                                subscription.status
                              )} text-white`}
                            >
                              {subscription.status.charAt(0).toUpperCase() +
                                subscription.status.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatDate(subscription.startDate)}
                          </TableCell>
                          <TableCell>
                            {subscription.endDate
                              ? formatDate(subscription.endDate)
                              : "N/A"}
                          </TableCell>
                          <TableCell>
                            {subscription.autoRenew ? "Yes" : "No"}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(subscription.plan.price)}
                            <span className="text-xs text-muted-foreground ml-1">
                              /
                              {getBillingCycleDisplay(
                                subscription.plan.billingCycle
                              ).toLowerCase()}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-6">
                          <p className="text-muted-foreground">
                            No subscriptions found.
                          </p>
                          <Button className="mt-4" asChild>
                            <Link
                              href={`/tenant/${tenant.slug}/subscriptions/new`}
                            >
                              Add First Subscription
                            </Link>
                          </Button>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
