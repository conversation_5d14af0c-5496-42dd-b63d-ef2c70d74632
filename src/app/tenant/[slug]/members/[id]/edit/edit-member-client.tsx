"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { updateMember } from "@/app/actions/members";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { memberSchema } from "@/lib/validations";

type MemberFormValues = z.infer<typeof memberSchema>;

interface EditMemberClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  member: any;
}

export default function EditMemberClient({
  user,
  tenant,
  tenants,
  member,
}: EditMemberClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize the form with existing member data
  const form = useForm<MemberFormValues>({
    resolver: zodResolver(memberSchema),
    defaultValues: {
      first_name: member.firstName || "",
      last_name: member.lastName || "",
      email: member.email || "",
      phone: member.phone || "",
      status: member.status || "active",
      notes: member.notes || "",
    },
  });

  // Handle form submission
  async function onSubmit(data: MemberFormValues) {
    setIsLoading(true);
    try {
      // Update the member using server action
      const result = await updateMember(member.id, tenant.id, data);

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("Member updated successfully");
      router.push(`/tenant/${tenant.slug}/members/${member.id}`);
    } catch (error) {
      console.error("Error updating member:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update member"
      );
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="mb-6">
              <Button
                variant="ghost"
                onClick={() =>
                  router.push(`/tenant/${tenant.slug}/members/${member.id}`)
                }
                className="mb-4"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Member Details
              </Button>
              <div>
                <h1 className="text-3xl font-bold">Edit Member</h1>
                <p className="text-muted-foreground">
                  Update member information for {member.firstName}{" "}
                  {member.lastName}
                </p>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Member Information</CardTitle>
                <CardDescription>
                  Update the details of the member
                </CardDescription>
              </CardHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="first_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First Name</FormLabel>
                            <FormControl>
                              <Input placeholder="John" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="last_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Doe" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone (Optional)</FormLabel>
                            <FormControl>
                              <Input
                                type="tel"
                                placeholder="+****************"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="inactive">Inactive</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Any additional notes about this member..."
                              className="resize-none"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Add any relevant notes or comments about this
                            member.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() =>
                        router.push(
                          `/tenant/${tenant.slug}/members/${member.id}`
                        )
                      }
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isLoading}>
                      {isLoading ? "Updating..." : "Update Member"}
                    </Button>
                  </CardFooter>
                </form>
              </Form>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
