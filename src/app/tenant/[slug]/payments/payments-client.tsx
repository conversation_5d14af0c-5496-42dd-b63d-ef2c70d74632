"use client";

import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { formatDate, formatCurrency } from "@/lib/utils";
import { PlusCircle, Search, Eye } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { Badge } from "@/components/ui/badge";

interface PaymentsClientProps {
  user: any;
  tenant: any;
  tenants: any[];
  payments: any[];
}

export default function PaymentsClient({
  user,
  tenant,
  tenants,
  payments,
}: PaymentsClientProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  // Filter payments based on search query
  const filteredPayments = payments.filter((payment) => {
    try {
      const memberName = payment.subscription?.member?.firstName
        ? `${payment.subscription.member.firstName} ${
            payment.subscription.member.lastName || ""
          }`.toLowerCase()
        : "";
      const planName = payment.subscription?.plan?.name
        ? payment.subscription.plan.name.toLowerCase()
        : "";
      const status = payment.status ? payment.status.toLowerCase() : "";
      const method = payment.paymentMethod
        ? payment.paymentMethod.toLowerCase()
        : "";
      const query = searchQuery.toLowerCase();

      return (
        memberName.includes(query) ||
        planName.includes(query) ||
        status.includes(query) ||
        method.includes(query)
      );
    } catch (error) {
      console.error("Error filtering payment:", error);
      return false;
    }
  });

  // Get payment status color
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "succeeded":
        return "bg-green-500";
      case "pending":
        return "bg-yellow-500";
      case "failed":
        return "bg-red-500";
      case "refunded":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Get payment method display
  const getPaymentMethodDisplay = (method: string) => {
    switch (method) {
      case "credit_card":
        return "Credit Card";
      case "debit_card":
        return "Debit Card";
      case "bank_transfer":
        return "Bank Transfer";
      case "cash":
        return "Cash";
      default:
        return method;
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader user={user} tenants={tenants} currentTenant={tenant} />
      <div className="flex flex-1 w-full">
        <DashboardSidebar tenantSlug={tenant.slug} />
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6 md:px-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold">Payments</h1>
                <p className="text-muted-foreground">
                  Manage payment transactions
                </p>
              </div>
              <Button asChild>
                <Link href={`/tenant/${tenant.slug}/payments/new`}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Payment
                </Link>
              </Button>
            </div>

            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-4">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search payments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-9 w-[300px]"
                />
              </div>
            </div>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle>All Payments</CardTitle>
                <CardDescription>
                  {filteredPayments.length} payments found
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Member</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPayments.length > 0 ? (
                      filteredPayments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell className="font-medium">
                            {payment.subscription?.member?.firstName
                              ? `${payment.subscription.member.firstName} ${
                                  payment.subscription.member.lastName || ""
                                }`
                              : "Unknown member"}
                          </TableCell>
                          <TableCell>
                            {payment.subscription?.plan?.name || "Unknown plan"}
                          </TableCell>
                          <TableCell>
                            {formatCurrency(payment.amount || 0)}
                          </TableCell>
                          <TableCell>
                            {payment.paymentDate
                              ? formatDate(payment.paymentDate)
                              : "N/A"}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={`${
                                payment.status
                                  ? getPaymentStatusColor(payment.status)
                                  : "bg-gray-500"
                              } text-white`}
                            >
                              {payment.status
                                ? `${payment.status
                                    .charAt(0)
                                    .toUpperCase()}${payment.status.slice(1)}`
                                : "Unknown"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {payment.paymentMethod
                              ? getPaymentMethodDisplay(payment.paymentMethod)
                              : "Unknown"}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() =>
                                router.push(
                                  `/tenant/${tenant.slug}/payments/${payment.id}`
                                )
                              }
                            >
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          No payments found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
