"use client";

import Link from "next/link";
import Image from "next/image";
import { formatCurrency } from "@/lib/utils";
import { Check } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface PublicPlansClientProps {
  tenant: any;
  plans: any[];
}

export default function PublicPlansClient({
  tenant,
  plans,
}: PublicPlansClientProps) {
  // Get billing cycle display
  const getBillingCycleDisplay = (cycle: string) => {
    switch (cycle) {
      case "monthly":
        return "per month";
      case "quarterly":
        return "per quarter";
      case "biannual":
        return "per 6 months";
      case "annual":
        return "per year";
      default:
        return `per ${cycle}`;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <header className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container mx-auto flex h-16 items-center px-4 py-4 md:px-6">
          <div className="flex items-center gap-2">
            {tenant.logo_url ? (
              <Image
                src={tenant.logo_url}
                alt={tenant.name}
                width={32}
                height={32}
                className="rounded-full"
              />
            ) : (
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold">
                {tenant.name.charAt(0)}
              </div>
            )}
            <span className="text-xl font-bold">{tenant.name}</span>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 md:px-6">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
            {tenant.public_page_title || "Membership Plans"}
          </h1>
          <p className="mt-4 text-lg text-muted-foreground">
            {tenant.public_page_description ||
              `Choose the perfect membership plan for you at ${tenant.name}`}
          </p>
        </div>

        {plans.length > 0 ? (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {plans.map((plan) => (
              <Card key={plan.id} className="flex flex-col">
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>
                    <div className="mt-2 flex items-baseline">
                      <span className="text-3xl font-bold tracking-tight">
                        {formatCurrency(plan.price)}
                      </span>
                      <span className="ml-1 text-sm text-muted-foreground">
                        {getBillingCycleDisplay(plan.billingCycle)}
                      </span>
                    </div>
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-1">
                  {plan.description && (
                    <p className="text-muted-foreground mb-4">
                      {plan.description}
                    </p>
                  )}
                  {plan.features && plan.features.length > 0 && (
                    <ul className="space-y-2">
                      {plan.features.map((feature: string, index: number) => (
                        <li key={index} className="flex items-center">
                          <Check className="mr-2 h-4 w-4 text-primary" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </CardContent>
                <CardFooter>
                  <Button className="w-full" asChild>
                    <Link href={`mailto:${tenant.contact_email || ""}`}>
                      Contact for Signup
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold mb-2">No Plans Available</h2>
            <p className="text-muted-foreground">
              There are currently no active membership plans available.
            </p>
          </div>
        )}
      </main>

      <footer className="border-t py-6 mt-12">
        <div className="container mx-auto px-4 md:px-6 text-center text-sm text-muted-foreground">
          <p>
            &copy; {new Date().getFullYear()} {tenant.name}. All rights
            reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
