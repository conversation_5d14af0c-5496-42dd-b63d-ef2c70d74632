import { createClient } from "@/utils/supabase/server";
import { prisma } from "./prisma";

/**
 * Get the current authenticated user from Supabase
 * This function maintains Supabase Auth while using Prisma for database queries
 */
export async function getCurrentUser() {
  const supabase = await createClient();
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user) {
    throw new Error("Authentication required");
  }

  return userData.user;
}

/**
 * Get the current user with their tenant associations using Prisma
 */
export async function getCurrentUserWithTenants() {
  const user = await getCurrentUser();

  // Get tenant associations for the user
  const tenantUsers = await prisma.tenantUser.findMany({
    where: { userId: user.id },
    include: {
      tenant: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  // Get tenants owned by the user
  const ownedTenants = await prisma.tenant.findMany({
    where: { ownerId: user.id },
    orderBy: {
      createdAt: "desc",
    },
  });

  return {
    ...user,
    tenantUsers,
    ownedTenants,
  };
}

/**
 * Verify that a user has access to a specific tenant
 */
export async function verifyTenantAccess(tenantId: string) {
  const user = await getCurrentUser();

  const tenantAccess = await prisma.tenantUser.findFirst({
    where: {
      userId: user.id,
      tenantId: tenantId,
    },
    include: {
      tenant: true,
    },
  });

  if (!tenantAccess) {
    // Check if user is the owner
    const ownedTenant = await prisma.tenant.findFirst({
      where: {
        id: tenantId,
        ownerId: user.id,
      },
    });

    if (!ownedTenant) {
      throw new Error("Access denied to this tenant");
    }

    return { tenant: ownedTenant, role: "owner" };
  }

  return { tenant: tenantAccess.tenant, role: tenantAccess.role };
}
