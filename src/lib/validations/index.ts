import { z } from "zod";

// Auth schemas
export const loginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters" }),
});

export const registerSchema = z
  .object({
    email: z.string().email({ message: "Please enter a valid email address" }),
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

// Tenant schemas
export const createTenantSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Business name must be at least 2 characters" }),
  slug: z
    .string()
    .min(2, { message: "Slug must be at least 2 characters" })
    .regex(/^[a-z0-9-]+$/, {
      message: "Slug can only contain lowercase letters, numbers, and hyphens",
    }),
});

// Member schemas
export const memberSchema = z.object({
  firstName: z
    .string()
    .min(2, { message: "First name must be at least 2 characters" }),
  lastName: z
    .string()
    .min(2, { message: "Last name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  phone: z.string().optional(),
  status: z.enum(["active", "inactive", "pending"], {
    required_error: "Please select a status",
  }),
  notes: z.string().optional(),
});

// Subscription plan schemas
export const subscriptionPlanSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Plan name must be at least 2 characters" }),
  description: z.string().optional(),
  price: z.coerce
    .number()
    .min(0.01, { message: "Price must be greater than 0" }),
  billingCycle: z.enum(["monthly", "quarterly", "yearly"], {
    required_error: "Please select a billing cycle",
  }),
  features: z.array(z.string()).optional(),
  isActive: z.boolean().default(true),
});

// Subscription schemas
export const subscriptionSchema = z.object({
  memberId: z.string().min(1, { message: "Please select a member" }),
  planId: z.string().min(1, { message: "Please select a plan" }),
  startDate: z.string(),
  endDate: z.string().optional(),
  status: z.enum(["active", "cancelled", "expired", "pending"], {
    required_error: "Please select a status",
  }),
  autoRenew: z.boolean().default(true),
});

// Payment schemas
export const paymentSchema = z.object({
  subscriptionId: z
    .string()
    .min(1, { message: "Please select a subscription" }),
  amount: z.coerce
    .number()
    .min(0.01, { message: "Amount must be greater than 0" }),
  paymentMethod: z.enum(
    ["credit_card", "debit_card", "bank_transfer", "cash"],
    {
      required_error: "Please select a payment method",
    }
  ),
});
